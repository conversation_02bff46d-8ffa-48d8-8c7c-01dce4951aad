import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { type SharedData } from '@/types';
import { Head, Link, usePage, router } from '@inertiajs/react';
import { Search, MapPin, Users, Star, Home, Shield, Clock } from 'lucide-react';
import { useState } from 'react';

export default function Welcome() {
    const { auth } = usePage<SharedData>().props;
    const [searchQuery, setSearchQuery] = useState('');

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        if (searchQuery.trim()) {
            // If user is authenticated, go to search page with query
            if (auth.user) {
                router.visit(route('pencari.search'), {
                    method: 'get',
                    data: { search: searchQuery.trim() }
                });
            } else {
                // If not authenticated, redirect to login first
                router.visit(route('login'));
            }
        }
    };

    return (
        <>
            <Head title="SIM Kost - Sistem Informasi Manajemen Kost">
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>
            
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
                {/* Navigation */}
                <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200 dark:bg-gray-900/80 dark:border-gray-700">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="flex justify-between items-center h-16">
                            <div className="flex items-center">
                                <Home className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                                <span className="ml-2 text-xl font-bold text-gray-900 dark:text-white">SIM Kost</span>
                            </div>
                            <div className="flex items-center space-x-4">
                                {auth.user ? (
                                    <Link
                                        href={route('dashboard')}
                                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                                    >
                                        Dashboard
                                    </Link>
                                ) : (
                                    <>
                                        <Link
                                            href={route('login')}
                                            className="text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 transition-colors"
                                        >
                                            Masuk
                                        </Link>
                                        <Link
                                            href={route('register')}
                                            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                                        >
                                            Daftar
                                        </Link>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                </nav>

                {/* Hero Section */}
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
                    <div className="text-center">
                        <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
                            Temukan <span className="text-blue-600 dark:text-blue-400">Kost Impian</span> Anda
                        </h1>
                        <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
                            Platform terpercaya untuk mencari dan mengelola kost dengan mudah. 
                            Dilengkapi dengan AI search untuk pengalaman yang lebih personal.
                        </p>
                        
                        {/* Search Bar */}
                        <div className="max-w-2xl mx-auto mb-12">
                            <form onSubmit={handleSearch} className="relative">
                                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                                <Input
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    placeholder="Cari kost berdasarkan lokasi, fasilitas, atau harga..."
                                    className="pl-12 pr-4 py-4 text-lg rounded-xl border-2 border-gray-200 focus:border-blue-500 dark:border-gray-600 dark:focus:border-blue-400"
                                />
                                <Button
                                    type="submit"
                                    className="absolute right-2 top-1/2 transform -translate-y-1/2 rounded-lg"
                                >
                                    Cari
                                </Button>
                            </form>
                        </div>

                        {/* Quick Stats */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                            <div className="text-center">
                                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">1000+</div>
                                <div className="text-gray-600 dark:text-gray-300">Kost Tersedia</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">50+</div>
                                <div className="text-gray-600 dark:text-gray-300">Kota</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">5000+</div>
                                <div className="text-gray-600 dark:text-gray-300">Pengguna Aktif</div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Features Section */}
                <div className="bg-white dark:bg-gray-900 py-20">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                                Mengapa Memilih SIM Kost?
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                                Platform yang dirancang khusus untuk memudahkan pencarian dan pengelolaan kost
                            </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
                                <CardHeader>
                                    <Search className="h-12 w-12 text-blue-600 dark:text-blue-400 mx-auto mb-4" />
                                    <CardTitle>AI-Powered Search</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <CardDescription>
                                        Cari kost dengan bahasa natural. AI kami akan memahami kebutuhan Anda dan memberikan rekomendasi terbaik.
                                    </CardDescription>
                                </CardContent>
                            </Card>

                            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
                                <CardHeader>
                                    <Shield className="h-12 w-12 text-blue-600 dark:text-blue-400 mx-auto mb-4" />
                                    <CardTitle>Terpercaya & Aman</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <CardDescription>
                                        Semua kost telah diverifikasi. Sistem keamanan berlapis untuk melindungi data dan transaksi Anda.
                                    </CardDescription>
                                </CardContent>
                            </Card>

                            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
                                <CardHeader>
                                    <Clock className="h-12 w-12 text-blue-600 dark:text-blue-400 mx-auto mb-4" />
                                    <CardTitle>Real-time Updates</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <CardDescription>
                                        Notifikasi real-time untuk inquiry, update ketersediaan kamar, dan komunikasi dengan pemilik kost.
                                    </CardDescription>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>

                {/* CTA Section */}
                <div className="bg-blue-600 dark:bg-blue-800 py-20">
                    <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
                        <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                            Siap Menemukan Kost Impian Anda?
                        </h2>
                        <p className="text-xl text-blue-100 mb-8">
                            Bergabunglah dengan ribuan pengguna yang telah menemukan kost terbaik melalui platform kami
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            {!auth.user && (
                                <>
                                    <Link href={route('register')}>
                                        <Button size="lg" variant="secondary" className="w-full sm:w-auto">
                                            Daftar Sebagai Pencari Kost
                                        </Button>
                                    </Link>
                                    <Link href={route('register')}>
                                        <Button size="lg" variant="outline" className="w-full sm:w-auto text-white border-white hover:bg-white hover:text-blue-600">
                                            Daftar Sebagai Pemilik Kost
                                        </Button>
                                    </Link>
                                </>
                            )}
                            {auth.user && (
                                <Link href={route('dashboard')}>
                                    <Button size="lg" variant="secondary" className="w-full sm:w-auto">
                                        Masuk ke Dashboard
                                    </Button>
                                </Link>
                            )}
                        </div>
                    </div>
                </div>

                {/* Footer */}
                <footer className="bg-gray-900 text-white py-12">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                            <div>
                                <div className="flex items-center mb-4">
                                    <Home className="h-8 w-8 text-blue-400" />
                                    <span className="ml-2 text-xl font-bold">SIM Kost</span>
                                </div>
                                <p className="text-gray-400">
                                    Platform terpercaya untuk mencari dan mengelola kost dengan mudah.
                                </p>
                            </div>
                            <div>
                                <h3 className="font-semibold mb-4">Untuk Pencari</h3>
                                <ul className="space-y-2 text-gray-400">
                                    <li>Cari Kost</li>
                                    <li>AI Search</li>
                                    <li>Favorit</li>
                                    <li>Riwayat Inquiry</li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="font-semibold mb-4">Untuk Pemilik</h3>
                                <ul className="space-y-2 text-gray-400">
                                    <li>Kelola Kost</li>
                                    <li>Manajemen Inquiry</li>
                                    <li>Statistik</li>
                                    <li>Promosi</li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="font-semibold mb-4">Bantuan</h3>
                                <ul className="space-y-2 text-gray-400">
                                    <li>FAQ</li>
                                    <li>Kontak</li>
                                    <li>Panduan</li>
                                    <li>Kebijakan</li>
                                </ul>
                            </div>
                        </div>
                        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                            <p>&copy; 2025 SIM Kost. All rights reserved.</p>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
