import React, { useState, useEffect } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { 
  ArrowLeft, 
  MapPin, 
  Users, 
  Calendar, 
  Wifi, 
  Car, 
  Utensils, 
  Bed, 
  Bath, 
  Shield, 
  Camera,
  MessageCircle,
  Heart,
  Share2,
  Star,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { formatCurrency } from '@/lib/utils';
import AppLayout from '@/layouts/app-layout';
import InquiryForm from '@/components/kost/inquiry-form';

interface KostDetailProps {
  kost: {
    id: number;
    name: string;
    description: string;
    address: string;
    city: string;
    province: string;
    price_monthly: number;
    gender_type: 'putra' | 'putri' | 'campur';
    kost_type: 'bulanan' | 'harian' | 'keduanya';
    available_rooms: number;
    room_count: number;
    images: Array<{
      id: number;
      image_path: string;
      image_type: string;
    }>;
    facilities: Array<{
      id: number;
      name: string;
      icon: string;
    }>;
    owner: {
      id: number;
      name: string;
      phone: string;
      email: string;
    };
    rules?: string[];
    created_at: string;
  };
  auth: {
    user: any;
  };
}

const facilityIcons: Record<string, any> = {
  'WiFi': Wifi,
  'Parkir Motor': Car,
  'Dapur Bersama': Utensils,
  'Kasur': Bed,
  'Kamar Mandi Dalam': Bath,
  'CCTV': Camera,
  'AC': Shield,
  'Lemari': Shield,
  'Meja Belajar': Shield,
  'Laundry': Shield,
};

export default function KostDetail({ kost, auth }: KostDetailProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isFavorite, setIsFavorite] = useState(false);
  const [showInquiryModal, setShowInquiryModal] = useState(false);

  // Check for showInquiry URL parameter
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('showInquiry') === 'true') {
      setShowInquiryModal(true);
    }
  }, []);

  // Check if kost data exists
  if (!kost) {
    return (
      <AppLayout>
        <Head title="Kost Tidak Ditemukan" />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Kost Tidak Ditemukan</h1>
            <p className="text-gray-600 mt-2">Kost yang Anda cari tidak tersedia.</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  const primaryImage = kost.images?.find(img => img.image_type === 'cover') || kost.images?.[0];
  const allImages = kost.images || [];

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % allImages.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + allImages.length) % allImages.length);
  };

  const handleInquiry = () => {
    setShowInquiryModal(true);
  };

  const handleFavorite = () => {
    setIsFavorite(!isFavorite);
    // TODO: Implement favorite API call
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: kost.name,
        text: kost.description,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
    }
  };

  const getGenderBadgeColor = (gender: string) => {
    switch (gender) {
      case 'putra': return 'bg-blue-100 text-blue-800';
      case 'putri': return 'bg-pink-100 text-pink-800';
      case 'campur': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentBadgeColor = (kostType: string) => {
    switch (kostType) {
      case 'harian': return 'bg-green-100 text-green-800';
      case 'bulanan': return 'bg-orange-100 text-orange-800';
      case 'keduanya': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <AppLayout>
      <Head title={`${kost.name} - Detail Kost`} />
      
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between py-4">
              <div className="flex items-center space-x-4">
                <Link
                  href="/pencari/search"
                  className="flex items-center text-gray-600 hover:text-gray-900"
                >
                  <ArrowLeft className="h-5 w-5 mr-2" />
                  Kembali ke Pencarian
                </Link>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleFavorite}
                  className={isFavorite ? 'text-red-600 border-red-600' : ''}
                >
                  <Heart className={`h-4 w-4 mr-2 ${isFavorite ? 'fill-current' : ''}`} />
                  {isFavorite ? 'Favorit' : 'Tambah Favorit'}
                </Button>
                <Button variant="outline" size="sm" onClick={handleShare}>
                  <Share2 className="h-4 w-4 mr-2" />
                  Bagikan
                </Button>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Image Gallery */}
              <Card>
                <CardContent className="p-0">
                  <div className="relative">
                    <img
                      src={allImages[currentImageIndex]?.image_path || '/images/placeholder-kost.jpg'}
                      alt={kost.name}
                      className="w-full h-96 object-cover rounded-t-lg"
                    />
                    {allImages.length > 1 && (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white"
                          onClick={prevImage}
                        >
                          <ChevronLeft className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white"
                          onClick={nextImage}
                        >
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                          {currentImageIndex + 1} / {allImages.length}
                        </div>
                      </>
                    )}
                  </div>
                  {allImages.length > 1 && (
                    <div className="p-4">
                      <div className="flex space-x-2 overflow-x-auto">
                        {allImages.map((image, index) => (
                          <button
                            key={image.id}
                            onClick={() => setCurrentImageIndex(index)}
                            className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${
                              index === currentImageIndex ? 'border-blue-500' : 'border-gray-200'
                            }`}
                          >
                            <img
                              src={image.image_path}
                              alt={`${kost.name} ${index + 1}`}
                              className="w-full h-full object-cover"
                            />
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Kost Info */}
              <Card>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-2xl font-bold">{kost.name}</CardTitle>
                      <div className="flex items-center text-gray-600 mt-2">
                        <MapPin className="h-4 w-4 mr-1" />
                        <span>{kost.address}, {kost.city}, {kost.province}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-3xl font-bold text-blue-600">
                        {formatCurrency(kost.price_monthly)}
                      </div>
                      <div className="text-sm text-gray-600">per bulan</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 mt-4">
                    <Badge className={getGenderBadgeColor(kost.gender_type)}>
                      <Users className="h-3 w-3 mr-1" />
                      {kost.gender_type ? kost.gender_type.charAt(0).toUpperCase() + kost.gender_type.slice(1) : 'N/A'}
                    </Badge>
                    <Badge className={getPaymentBadgeColor(kost.kost_type)}>
                      <Calendar className="h-3 w-3 mr-1" />
                      {kost.kost_type ? kost.kost_type.charAt(0).toUpperCase() + kost.kost_type.slice(1) : 'N/A'}
                    </Badge>
                    <Badge variant="outline">
                      {kost.available_rooms} kamar tersisa
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div>
                    <h3 className="font-semibold mb-2">Deskripsi</h3>
                    <p className="text-gray-700 leading-relaxed">{kost.description}</p>
                  </div>
                </CardContent>
              </Card>

              {/* Facilities */}
              <Card>
                <CardHeader>
                  <CardTitle>Fasilitas</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {kost.facilities.map((facility) => {
                      const IconComponent = facilityIcons[facility.name] || Shield;
                      return (
                        <div key={facility.id} className="flex items-center space-x-2">
                          <IconComponent className="h-5 w-5 text-blue-600" />
                          <span className="text-sm">{facility.name}</span>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>

              {/* Rules */}
              {kost.rules && Array.isArray(kost.rules) && kost.rules.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Peraturan Kost</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {kost.rules.map((rule, index) => (
                        <li key={index} className="flex items-start">
                          <span className="text-blue-600 mr-2">•</span>
                          <span className="text-sm text-gray-700">{rule}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Owner Info */}
              <Card>
                <CardHeader>
                  <CardTitle>Informasi Pemilik</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <div className="font-medium">{kost.owner.name}</div>
                      <div className="text-sm text-gray-600">{kost.owner.phone}</div>
                      <div className="text-sm text-gray-600">{kost.owner.email}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Action Buttons */}
              <Card>
                <CardContent className="p-6">
                  <div className="space-y-3">
                    <Button 
                      onClick={handleInquiry}
                      className="w-full"
                      size="lg"
                    >
                      <MessageCircle className="h-5 w-5 mr-2" />
                      Tanya Kost
                    </Button>
                    <Button 
                      variant="outline" 
                      className="w-full"
                      size="lg"
                    >
                      <span>Hubungi Pemilik</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Availability */}
              <Card>
                <CardHeader>
                  <CardTitle>Ketersediaan</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Kamar</span>
                      <span className="font-medium">{kost.room_count || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Kamar Tersedia</span>
                      <span className="font-medium text-green-600">{kost.available_rooms || 0}</span>
                    </div>
                    <Separator />
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Status</span>
                      <Badge variant={(kost.available_rooms || 0) > 0 ? "default" : "destructive"}>
                        {(kost.available_rooms || 0) > 0 ? 'Tersedia' : 'Penuh'}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Inquiry Modal */}
      <Dialog open={showInquiryModal} onOpenChange={setShowInquiryModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Kirim Pertanyaan tentang {kost.name}</DialogTitle>
          </DialogHeader>
          <InquiryForm
            kost={kost}
            onSuccess={() => {
              setShowInquiryModal(false);
              // You can add a success toast here if needed
            }}
            onCancel={() => setShowInquiryModal(false)}
          />
        </DialogContent>
      </Dialog>
    </AppLayout>
  );
}
